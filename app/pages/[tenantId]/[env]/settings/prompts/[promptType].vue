<script setup lang="ts">
import { PROMPT_TYPE_LABELS } from '~/constants/prompts'
import type { CreatePromptPayload, UpdatePromptPayload } from '~/types/index.d'

const route = useRoute()

// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['manage_prompts'],
  breadcrumbs: [
    {
      label: 'プロンプト設定',
      icon: 'material-symbols:chat-bubble-outline'
    }
  ]
})

const { selectedTenantId, selectedEnvId } = useApp()
const promptsStore = usePromptsStore()
const { loadings, errors } = storeToRefs(promptsStore)
const toast = useToast()

// Get prompt type from route
const promptType = computed(() => Number(route.params.promptType))
const promptTypeLabel = computed(() => PROMPT_TYPE_LABELS[promptType.value] || `Type ${promptType.value}`)

// Form state
const isEditing = ref(false)
const prompt = ref<any>(null)
const originalPrompt = ref<any>(null)

// Form data
const formData = ref({
  system_message: '',
  template: '',
  input_variables: [] as string[],
  optional_variables: [] as string[],
  enabled: true
})

// Load prompt data
const loadPrompt = async () => {
  const result = await promptsStore.getPrompt(selectedTenantId.value, selectedEnvId.value, promptType.value)
  if (result) {
    prompt.value = result
    originalPrompt.value = { ...result }
    formData.value = {
      system_message: result.system_message,
      template: result.template,
      input_variables: [...result.input_variables],
      optional_variables: result.optional_variables || [],
      enabled: result.enabled
    }
  } else {
    // New prompt
    prompt.value = null
    originalPrompt.value = null
    formData.value = {
      system_message: '',
      template: '',
      input_variables: [],
      optional_variables: [],
      enabled: true
    }
  }
}

// Load data on mount
onMounted(() => {
  loadPrompt()
})

// Watch route changes
watch(() => route.params.promptType, () => {
  loadPrompt()
})

// Form validation
const isFormValid = computed(() => {
  return formData.value.system_message.trim() !== '' && formData.value.template.trim() !== ''
})

// Check if form has changes
const hasChanges = computed(() => {
  if (!originalPrompt.value) return true // New prompt

  return (
    formData.value.system_message !== originalPrompt.value.system_message
    || formData.value.template !== originalPrompt.value.template
    || JSON.stringify(formData.value.input_variables) !== JSON.stringify(originalPrompt.value.input_variables)
    || JSON.stringify(formData.value.optional_variables) !== JSON.stringify(originalPrompt.value.optional_variables)
    || formData.value.enabled !== originalPrompt.value.enabled
  )
})

// Save prompt
const savePrompt = async () => {
  if (!isFormValid.value) return

  try {
    let result
    if (prompt.value) {
      // Update existing prompt
      const updatePayload: UpdatePromptPayload = {
        system_message: formData.value.system_message,
        template: formData.value.template,
        input_variables: formData.value.input_variables,
        optional_variables: formData.value.optional_variables,
        enabled: formData.value.enabled
      }
      result = await promptsStore.updatePrompt(selectedTenantId.value, selectedEnvId.value, promptType.value, updatePayload)
    } else {
      // Create new prompt
      const createPayload: CreatePromptPayload = {
        system_message: formData.value.system_message,
        template: formData.value.template,
        input_variables: formData.value.input_variables,
        optional_variables: formData.value.optional_variables,
        enabled: formData.value.enabled,
        prompt_type: promptType.value
      }
      result = await promptsStore.createPrompt(selectedTenantId.value, selectedEnvId.value, createPayload)
    }

    if (result) {
      await loadPrompt() // Reload data
      isEditing.value = false
      toast.add({
        title: 'プロンプトを保存しました',
        color: 'green'
      })
    }
  } catch (error) {
    toast.add({
      title: 'エラーが発生しました',
      description: 'プロンプトの保存に失敗しました',
      color: 'red'
    })
  }
}

// Delete prompt
const deletePrompt = async () => {
  if (!prompt.value) return

  const isConfirmed = confirm(`プロンプト「${promptTypeLabel.value}」を削除しますか？`)

  if (isConfirmed) {
    const result = await promptsStore.deletePrompt(selectedTenantId.value, selectedEnvId.value, promptType.value)
    if (result) {
      toast.add({
        title: 'プロンプトを削除しました',
        color: 'green'
      })
      await loadPrompt() // Reload data
    }
  }
}

// Cancel editing
const cancelEdit = () => {
  if (originalPrompt.value) {
    formData.value = {
      system_message: originalPrompt.value.system_message,
      template: originalPrompt.value.template,
      input_variables: [...originalPrompt.value.input_variables],
      optional_variables: originalPrompt.value.optional_variables || [],
      enabled: originalPrompt.value.enabled
    }
  }
  isEditing.value = false
}

// Add/remove variables
const addInputVariable = () => {
  formData.value.input_variables.push('')
}

const removeInputVariable = (index: number) => {
  formData.value.input_variables.splice(index, 1)
}

const addOptionalVariable = () => {
  formData.value.optional_variables.push('')
}

const removeOptionalVariable = (index: number) => {
  formData.value.optional_variables.splice(index, 1)
}

// Navigate back
const goBack = () => {
  navigateTo(`/${selectedTenantId.value}/${selectedEnvId.value}/settings/prompts`)
}
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <UDashboardSection
      :title="`プロンプト設定: ${promptTypeLabel}`"
      :description="`プロンプトタイプ ${promptType} の設定`"
    >
      <template #links>
        <UButton
          icon="i-heroicons-arrow-left"
          variant="outline"
          size="xs"
          @click="goBack"
        >
          戻る
        </UButton>
      </template>

      <!-- Loading state -->
      <div
        v-if="loadings.getPrompt"
        class="space-y-4"
      >
        <USkeleton class="h-32 w-full" />
        <USkeleton class="h-48 w-full" />
      </div>

      <!-- Error state -->
      <UAlert
        v-else-if="errors.getPrompt"
        icon="i-heroicons-exclamation-triangle"
        color="red"
        variant="soft"
        title="エラーが発生しました"
        :description="errors.getPrompt?.message || 'プロンプトの読み込みに失敗しました'"
        class="mb-4"
      />

      <!-- Form -->
      <div
        v-else
        class="space-y-6"
      >
        <!-- Status and Actions -->
        <UCard>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <UBadge
                :color="prompt ? (prompt.enabled ? 'green' : 'orange') : 'gray'"
                variant="soft"
              >
                {{ prompt ? (prompt.enabled ? '有効' : '無効') : '未設定' }}
              </UBadge>

              <div
                v-if="prompt"
                class="text-sm text-gray-500"
              >
                <div>作成者: {{ prompt.created_username }}</div>
                <div>作成日: {{ new Date(prompt.created_at).toLocaleString('ja-JP') }}</div>
                <div v-if="prompt.updated_at !== prompt.created_at">
                  最終更新: {{ new Date(prompt.updated_at).toLocaleString('ja-JP') }} ({{ prompt.updated_username }})
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <UButton
                v-if="!isEditing"
                icon="i-heroicons-pencil"
                variant="outline"
                size="sm"
                @click="isEditing = true"
              >
                編集
              </UButton>

              <UButton
                v-if="prompt && !isEditing"
                icon="i-heroicons-trash"
                color="red"
                variant="outline"
                size="sm"
                :loading="loadings.deletePrompt"
                @click="deletePrompt"
              >
                削除
              </UButton>
            </div>
          </div>
        </UCard>

        <!-- Form Fields -->
        <UCard>
          <div class="space-y-6">
            <!-- System Message -->
            <UFormGroup
              label="システムメッセージ"
              required
            >
              <UTextarea
                v-model="formData.system_message"
                :readonly="!isEditing"
                placeholder="システムメッセージを入力してください"
                :rows="4"
              />
            </UFormGroup>

            <!-- Template -->
            <UFormGroup
              label="テンプレート"
              required
            >
              <UTextarea
                v-model="formData.template"
                :readonly="!isEditing"
                placeholder="テンプレートを入力してください（例: {question}を使用して変数を指定）"
                :rows="6"
              />
            </UFormGroup>

            <!-- Input Variables -->
            <UFormGroup label="必須変数">
              <div class="space-y-2">
                <div
                  v-for="(variable, index) in formData.input_variables"
                  :key="index"
                  class="flex items-center space-x-2"
                >
                  <UInput
                    v-model="formData.input_variables[index]"
                    :readonly="!isEditing"
                    placeholder="変数名"
                    class="flex-1"
                  />
                  <UButton
                    v-if="isEditing"
                    icon="i-heroicons-trash"
                    color="red"
                    variant="ghost"
                    size="sm"
                    @click="removeInputVariable(index)"
                  />
                </div>

                <UButton
                  v-if="isEditing"
                  icon="i-heroicons-plus"
                  variant="outline"
                  size="sm"
                  @click="addInputVariable"
                >
                  必須変数を追加
                </UButton>
              </div>
            </UFormGroup>

            <!-- Optional Variables -->
            <UFormGroup label="オプション変数">
              <div class="space-y-2">
                <div
                  v-for="(variable, index) in formData.optional_variables"
                  :key="index"
                  class="flex items-center space-x-2"
                >
                  <UInput
                    v-model="formData.optional_variables[index]"
                    :readonly="!isEditing"
                    placeholder="変数名"
                    class="flex-1"
                  />
                  <UButton
                    v-if="isEditing"
                    icon="i-heroicons-trash"
                    color="red"
                    variant="ghost"
                    size="sm"
                    @click="removeOptionalVariable(index)"
                  />
                </div>

                <UButton
                  v-if="isEditing"
                  icon="i-heroicons-plus"
                  variant="outline"
                  size="sm"
                  @click="addOptionalVariable"
                >
                  オプション変数を追加
                </UButton>
              </div>
            </UFormGroup>

            <!-- Enabled Toggle -->
            <UFormGroup label="有効/無効">
              <UToggle
                v-model="formData.enabled"
                :disabled="!isEditing"
              />
            </UFormGroup>
          </div>
        </UCard>

        <!-- Action Buttons -->
        <div
          v-if="isEditing"
          class="flex items-center justify-end space-x-3"
        >
          <UButton
            variant="outline"
            @click="cancelEdit"
          >
            キャンセル
          </UButton>

          <UButton
            icon="i-heroicons-check"
            :disabled="!isFormValid || !hasChanges"
            :loading="loadings.createPrompt || loadings.updatePrompt"
            @click="savePrompt"
          >
            保存
          </UButton>
        </div>
      </div>
    </UDashboardSection>
  </UDashboardPanelContent>
</template>
