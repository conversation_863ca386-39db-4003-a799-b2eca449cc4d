<script setup lang="ts">
import { PROMPT_TYPE_LABELS, PROMPT_TYPE_ICONS } from '~/constants/prompts'

// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_prompts']
})

const { selectedTenantId, selectedEnvId } = useApp()
const promptsStore = usePromptsStore()

// Get all available prompt types (static list, no API calls)
const promptTypesList = computed(() => {
  return Object.entries(PROMPT_TYPE_LABELS)
    .map(([type, label]) => {
      const promptType = Number(type)
      const prompt = promptsStore.getPromptByType(promptType)

      return {
        type: promptType,
        label,
        exists: !!prompt,
        enabled: prompt?.enabled || false,
        lastUpdated: prompt?.updated_at,
        updatedBy: prompt?.updated_username
      }
    })
    .sort((a, b) => a.type - b.type)
})

// Navigate to prompt detail page
const navigateToPrompt = (promptType: number) => {
  navigateTo(
    `/${selectedTenantId.value}/${selectedEnvId.value}/settings/prompts/${promptType}`
  )
}

// Get status badge color
const getStatusColor = (item: any) => {
  if (!item.exists) return 'gray'
  return item.enabled ? 'green' : 'orange'
}

// Get status text
const getStatusText = (item: any) => {
  if (!item.exists) return '未設定'
  return item.enabled ? '有効' : '無効'
}

// Get icon for prompt type
const getPromptIcon = (promptType: number) => {
  return (
    PROMPT_TYPE_ICONS[promptType] || 'i-heroicons-chat-bubble-left-ellipsis'
  )
}
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <UDashboardSection
      title="プロンプト管理"
      description="各プロンプトタイプの設定を管理します"
    >
      <!-- Prompt types list -->
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        <UCard
          v-for="item in promptTypesList"
          :key="item.type"
          class="h-full cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
          @click="navigateToPrompt(item.type)"
        >
          <div class="flex items-center justify-between">
            <div class="flex flex-row items-center space-x-4">
              <UIcon
                :name="getPromptIcon(item.type)"
                class="w-5 h-5 text-gray-400"
              />

              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-3">
                  <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ item.label }}
                  </h3>
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <UBadge
                variant="soft"
                size="xs"
              >
                Type: {{ item.type }}
              </UBadge>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Empty state -->
      <UCard
        v-if="promptTypesList.length === 0"
        class="text-center py-12"
      >
        <UIcon
          name="i-heroicons-chat-bubble-left-ellipsis"
          class="w-12 h-12 text-gray-400 mx-auto mb-4"
        />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          プロンプトが見つかりません
        </h3>
        <p class="text-gray-500">
          設定可能なプロンプトタイプがありません
        </p>
      </UCard>
    </UDashboardSection>
  </UDashboardPanelContent>
</template>
