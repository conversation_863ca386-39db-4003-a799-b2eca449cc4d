import type {
  Prompt,
  CreatePromptPayload,
  UpdatePromptPayload,
  PromptDeleteResponse
} from '~/types/index.d'
import { PROMPT_TYPE_LABELS, PromptType } from '~/constants/prompts'

export const usePromptsStore = defineStore('promptsStore', {
  state: () => ({
    prompts: {} as Record<number, Prompt>, // prompt_type -> prompt
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    promptForm: false,
    editingPrompt: null as Prompt | null
  }),
  getters: {
    getAllPrompts: state => (): Prompt[] => {
      return Object.values(state.prompts)
    },

    getPromptByType: state => (prompt_type: number): Prompt | undefined => {
      return state.prompts[prompt_type]
    },

    getEnabledPrompts: state => (): Prompt[] => {
      return Object.values(state.prompts).filter(prompt => prompt.enabled)
    },

    getDisabledPrompts: state => (): Prompt[] => {
      return Object.values(state.prompts).filter(prompt => !prompt.enabled)
    },

    getAllPromptTypes: state => (): number[] => {
      return Object.keys(state.prompts).map(Number).sort()
    },

    getPromptTypeLabel: () => (prompt_type: number): string => {
      return PROMPT_TYPE_LABELS[prompt_type] || `Unknown Type ${prompt_type}`
    },

    promptsForDropdown: state => () => {
      return Object.values(state.prompts).map(prompt => ({
        label: PROMPT_TYPE_LABELS[prompt.prompt_type] || `Type ${prompt.prompt_type}`,
        value: prompt.prompt_type,
        description: prompt.system_message.substring(0, 100) + (prompt.system_message.length > 100 ? '...' : '')
      }))
    },

    getPromptsByEnabled: state => (enabled: boolean): Prompt[] => {
      return Object.values(state.prompts).filter(prompt => prompt.enabled === enabled)
    }
  },
  actions: {
    // Get a specific prompt by prompt_type
    async getPrompt(tenant_id: string, env_id: string, prompt_type: number): Promise<Prompt | false> {
      try {
        this.loadings.getPrompt = true
        this.errors.getPrompt = null

        const response = await useAPI().adminService.get(
          `/v2/prompts/${prompt_type}/tenants/${tenant_id}/env/${env_id}`
        )

        // Store the prompt in the state
        this.prompts[prompt_type] = response.data

        return response.data
      } catch (error: any) {
        this.errors.getPrompt = error?.response?.data || error
        return false
      } finally {
        this.loadings.getPrompt = false
      }
    },

    // Create a new prompt
    async createPrompt(tenant_id: string, env_id: string, payload: CreatePromptPayload): Promise<Prompt | false> {
      try {
        this.loadings.createPrompt = true
        this.errors.createPrompt = null

        const response = await useAPI().adminService.post(
          `/v2/prompts/tenants/${tenant_id}/env/${env_id}`,
          payload
        )

        // Store the new prompt in the state
        this.prompts[payload.prompt_type] = response.data

        return response.data
      } catch (error: any) {
        this.errors.createPrompt = error?.response?.data || error
        return false
      } finally {
        this.loadings.createPrompt = false
      }
    },

    // Update an existing prompt
    async updatePrompt(tenant_id: string, env_id: string, prompt_type: number, payload: UpdatePromptPayload): Promise<Prompt | false> {
      try {
        this.loadings.updatePrompt = true
        this.errors.updatePrompt = null

        const response = await useAPI().adminService.put(
          `/v2/prompts/${prompt_type}/tenants/${tenant_id}/env/${env_id}`,
          payload
        )

        // Update the prompt in the store
        this.prompts[prompt_type] = response.data

        return response.data
      } catch (error: any) {
        this.errors.updatePrompt = error?.response?.data || error
        return false
      } finally {
        this.loadings.updatePrompt = false
      }
    },

    // Delete a prompt
    async deletePrompt(tenant_id: string, env_id: string, prompt_type: number): Promise<PromptDeleteResponse | false> {
      try {
        this.loadings.deletePrompt = true
        this.errors.deletePrompt = null

        const response = await useAPI().adminService.delete(
          `/v2/prompts/${prompt_type}/tenants/${tenant_id}/env/${env_id}`
        )

        // Remove the prompt from the store
        if (this.prompts[prompt_type]) {
          const { [prompt_type]: removed, ...rest } = this.prompts
          this.prompts = rest
        }

        return response.data
      } catch (error: any) {
        this.errors.deletePrompt = error?.response?.data || error
        return false
      } finally {
        this.loadings.deletePrompt = false
      }
    },

    // Toggle prompt enabled status
    async togglePromptStatus(tenant_id: string, env_id: string, prompt_type: number): Promise<Prompt | false> {
      const prompt = this.getPromptByType(prompt_type)
      if (!prompt) return false

      return await this.updatePrompt(tenant_id, env_id, prompt_type, {
        enabled: !prompt.enabled
      })
    },

    // Bulk operations
    async bulkDeletePrompts(tenant_id: string, env_id: string, prompt_types: number[]): Promise<boolean> {
      try {
        this.loadings.bulkDeletePrompts = true
        this.errors.bulkDeletePrompts = null

        const promises = prompt_types.map(type =>
          this.deletePrompt(tenant_id, env_id, type)
        )

        const results = await Promise.all(promises)
        return results.every(result => result !== false)
      } catch (error: any) {
        this.errors.bulkDeletePrompts = error?.response?.data || error
        return false
      } finally {
        this.loadings.bulkDeletePrompts = false
      }
    },

    async bulkTogglePromptStatus(tenant_id: string, env_id: string, prompt_types: number[], enabled: boolean): Promise<boolean> {
      try {
        this.loadings.bulkTogglePromptStatus = true
        this.errors.bulkTogglePromptStatus = null

        const promises = prompt_types.map(type =>
          this.updatePrompt(tenant_id, env_id, type, { enabled })
        )

        const results = await Promise.all(promises)
        return results.every(result => result !== false)
      } catch (error: any) {
        this.errors.bulkTogglePromptStatus = error?.response?.data || error
        return false
      } finally {
        this.loadings.bulkTogglePromptStatus = false
      }
    },

    // Clear all prompts
    clearAllPrompts() {
      this.prompts = {}
    },

    // Clear specific prompt by type
    clearPrompt(prompt_type: number) {
      if (this.prompts[prompt_type]) {
        const { [prompt_type]: removed, ...rest } = this.prompts
        this.prompts = rest
      }
    },

    // Search prompts by system_message or template content
    searchPrompts(searchTerm: string): Prompt[] {
      const prompts = Object.values(this.prompts)
      if (!searchTerm.trim()) return prompts

      const term = searchTerm.toLowerCase()
      return prompts.filter(prompt =>
        prompt.system_message.toLowerCase().includes(term)
        || prompt.template.toLowerCase().includes(term)
        || prompt.input_variables.some(variable => variable.toLowerCase().includes(term))
        || prompt.optional_variables.some(variable => variable.toLowerCase().includes(term))
        || PROMPT_TYPE_LABELS[prompt.prompt_type]?.toLowerCase().includes(term)
      )
    },

    // Get prompts statistics
    getPromptsStats() {
      const prompts = Object.values(this.prompts)
      return {
        total: prompts.length,
        enabled: prompts.filter(p => p.enabled).length,
        disabled: prompts.filter(p => !p.enabled).length,
        promptTypes: Object.keys(this.prompts).map(Number).sort(),
        byType: Object.keys(this.prompts).reduce((acc, type) => {
          const typeNum = Number(type)
          const prompt = this.prompts[typeNum]
          if (prompt) {
            acc[typeNum] = {
              label: PROMPT_TYPE_LABELS[typeNum] || `Type ${typeNum}`,
              enabled: prompt.enabled
            }
          }
          return acc
        }, {} as Record<number, { label: string, enabled: boolean }>)
      }
    },

    // Duplicate a prompt with a new prompt_type
    async duplicatePrompt(tenant_id: string, env_id: string, source_prompt_type: number, new_prompt_type: number): Promise<Prompt | false> {
      const originalPrompt = this.getPromptByType(source_prompt_type)
      if (!originalPrompt) return false

      // Check if target prompt type already exists
      if (this.prompts[new_prompt_type]) {
        this.errors.duplicatePrompt = { message: `Prompt type ${new_prompt_type} already exists` }
        return false
      }

      const duplicatePayload: CreatePromptPayload = {
        system_message: originalPrompt.system_message,
        template: originalPrompt.template,
        input_variables: [...originalPrompt.input_variables],
        optional_variables: originalPrompt.optional_variables,
        enabled: false, // Set duplicated prompts as disabled by default
        prompt_type: new_prompt_type
      }

      return await this.createPrompt(tenant_id, env_id, duplicatePayload)
    },

    // Load all available prompt types for current tenant/env
    async loadAllPrompts(tenant_id: string, env_id: string): Promise<boolean> {
      try {
        this.loadings.loadAllPrompts = true
        this.errors.loadAllPrompts = null

        // Load all known prompt types
        const promptTypes = Object.values(PromptType).filter(value => typeof value === 'number') as number[]
        const promises = promptTypes.map(type =>
          this.getPrompt(tenant_id, env_id, type).catch(() => null) // Ignore errors for non-existing prompts
        )

        await Promise.all(promises)
        return true
      } catch (error: any) {
        this.errors.loadAllPrompts = error?.response?.data || error
        return false
      } finally {
        this.loadings.loadAllPrompts = false
      }
    }
  }
})
