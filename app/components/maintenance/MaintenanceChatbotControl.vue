<script setup lang="ts">
import type {
  MaintenanceStatus,
  MaintenanceActivationPayload
} from '~/types/index.d'

interface Props {
  tenant: any
  showAllControls?: boolean // Show operator-only controls
}

const props = withDefaults(defineProps<Props>(), {
  showAllControls: false
})

const endDateError = ref('')
const startDateError = ref('')

const maintenanceStore = useMaintenanceStore()
const authStore = useAuthStore()
const confirm = useConfirm()

const { loadings, systemMaintenanceStatus }
  = storeToRefs(maintenanceStore)

// Check if current user can manage maintenance
const canManageTenant = computed(() => {
  return authStore.isAdmin || authStore.isOperator
})

// Get maintenance status for current tenant
const currentStatus = computed((): MaintenanceStatus | null => {
  return maintenanceStore.getMaintenanceStatus(props.tenant?.id)
})

const isActive = computed(() => {
  const status = currentStatus.value
  return status ? isMaintenanceCurrentlyActive(status) : false
})

// Check if maintenance is scheduled but not currently active (future scheduled maintenance)
const isScheduledButNotActive = computed(() => {
  const status = currentStatus.value
  if (!status?.maintenance) return false

  // If it's currently active, it's not "scheduled but not active"
  if (isActive.value) return false

  // If there's no start date, it's indefinite (not scheduled)
  if (!status.start_date) return false

  // Check if start date is in the future
  const now = new Date()
  const start = new Date(status.start_date)

  return start > now
})

// Form state for maintenance activation
// Helper function to get current Japan time for datetime-local input
const toJapanLocalDateTime = (date: Date): string => {
  // Convert to Japan timezone (UTC+9) and format for datetime-local input
  const japanTime = new Date(
    date.toLocaleString('en-US', { timeZone: 'Asia/Tokyo' })
  )

  // Format for datetime-local input (YYYY-MM-DDTHH:mm)
  const year = japanTime.getFullYear()
  const month = String(japanTime.getMonth() + 1).padStart(2, '0')
  const day = String(japanTime.getDate()).padStart(2, '0')
  const hours = String(japanTime.getHours()).padStart(2, '0')
  const minutes = String(japanTime.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day}T${hours}:${minutes}`
}

const maintenanceForm = reactive({
  message: 'ただいまメンテナンス中です。\nしばらくしてもう一度お試しください。',
  mode: 'indefinite' as 'indefinite' | 'scheduled', // New: maintenance mode
  start_date: toJapanLocalDateTime(new Date()), // Japan local time for datetime-local input
  end_date: toJapanLocalDateTime(new Date(Date.now() + 60 * 60 * 1000)) // 1 hours from now in Japan time
})

// Auto-refresh interval for maintenance status
let refreshInterval: ReturnType<typeof setInterval> | null = null

// Load maintenance status on mount
onMounted(async () => {
  if (canManageTenant.value) {
    await maintenanceStore.fetchMaintenanceStatus(props.tenant?.id)
    await maintenanceStore.checkSystemMaintenance()
  }

  // Set up auto-refresh every 30 seconds to keep status in sync
  refreshInterval = setInterval(async () => {
    if (canManageTenant.value && props.tenant?.id) {
      await maintenanceStore.fetchMaintenanceStatus(props.tenant.id)
      await maintenanceStore.checkSystemMaintenance()
    }
  }, 60000)
})

// Clean up interval on unmount
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})

// Handle tenant-specific maintenance toggle
const handleToggleTenant = async () => {
  startDateError.value = ''
  endDateError.value = ''

  const action = isActive.value ? '無効化' : '有効化'
  const actionDescription = isActive.value
    ? 'メンテナンスモードを無効にします。\nユーザーは通常通りチャットボットのサービスを利用できるようになります。\nよろしいですか?'
    : 'メンテナンスモードを有効にします。\nユーザーはチャットボットのサービスを利用できなくなります。\nよろしいですか?'

  // check if action is turn on
  if (!isActive.value && maintenanceForm.mode === 'scheduled') {
    // start date is not valid or not provided
    if (!maintenanceForm.start_date) {
      startDateError.value = '開始日時は必須です。'
      return
    }
    // but end date is in the past
    if (
      maintenanceForm.end_date
      && new Date(maintenanceForm.end_date) < new Date()
    ) {
      endDateError.value = '終了日時は現在以降の日時を指定してください。'
      return
    }

    // if end date is provided but before start date
    if (
      maintenanceForm.end_date
      && new Date(maintenanceForm.end_date) < new Date(maintenanceForm.start_date)
    ) {
      endDateError.value = '終了日時は開始日時以降の日時を指定してください。'
      return
    }
  }

  confirm.show({
    title: `チャットボットのメンテナンス${action}の確認`,
    description: `「${props.tenant?.description}」のチャットボットの${actionDescription}`,
    confirmText: action,
    cancelText: 'キャンセル',
    onConfirm: async () => {
      try {
        const payload: MaintenanceActivationPayload = {
          message: maintenanceForm.message
        }

        // Add dates only for scheduled maintenance
        if (maintenanceForm.mode === 'scheduled') {
          // Send Japan time directly to API (no UTC conversion)
          payload.start_date = formatDateTime(
            new Date(maintenanceForm.start_date)
          )
          payload.end_date = formatDateTime(new Date(maintenanceForm.end_date))
        }

        await maintenanceStore.toggleTenantMaintenance(
          props.tenant?.id,
          payload
        )

        // Show success message
        const toast = useToast()
        toast.add({
          title: 'メンテナンス設定',
          description: `テナント「${props.tenant?.description}」のメンテナンスモードを${action}しました`,
          color: 'green'
        })
      } catch (error) {
        console.error('Failed to toggle maintenance:', error)

        const toast = useToast()
        toast.add({
          title: 'エラー',
          description: 'メンテナンス設定の変更に失敗しました',
          color: 'red'
        })
      }
    }
  })
}

// Format date for display
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('ja-JP')
}

// Format date to YYYY-MM-DD HH:mm:ss format for API
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// Helper function to check if maintenance is currently active
const isMaintenanceCurrentlyActive = (status: MaintenanceStatus): boolean => {
  if (!status.maintenance) return false

  // Indefinite maintenance is always active if maintenance flag is true
  if (!status.start_date || !status.end_date) return true

  // For scheduled maintenance, check if current time is between start and end
  const now = new Date()
  const start = new Date(status.start_date)
  const end = new Date(status.end_date)

  return now >= start && now <= end
}

// Helper function to get appropriate countdown message
const getCountdownMessage = (status: MaintenanceStatus): string => {
  if (!status.end_date) {
    return '継続時間'
  }

  const isCurrentlyActive = isMaintenanceCurrentlyActive(status)
  if (isCurrentlyActive) {
    return '終了まで'
  } else {
    return '開始まで'
  }
}

// Helper function to get status badge color
const getStatusBadgeColor = () => {
  if (!currentStatus.value?.maintenance) return 'green'

  if (isActive.value) {
    return 'red' // Currently in maintenance
  } else {
    if (
      currentStatus.value.end_date
      && new Date(currentStatus.value.end_date) < new Date()
    ) {
      return 'green'
    }
    return 'blue' // Scheduled but not started yet
  }
}

// Helper function to get status badge label
const getStatusBadgeLabel = (): string => {
  if (!currentStatus.value?.maintenance) return '稼働中'
  // if end_date is in the past, consider it expired
  if (
    currentStatus.value.end_date
    && new Date(currentStatus.value.end_date) < new Date()
  ) {
    return '稼働中'
  }

  if (isActive.value) {
    return 'メンテナンス中'
  } else {
    return 'メンテナンス予定'
  }
}

// Get the system maintenance status for display
const globalMaintenanceStatus = computed((): MaintenanceStatus | null => {
  return systemMaintenanceStatus.value
})

// Check if global maintenance is scheduled but not active
const isGlobalScheduledButNotActive = computed(() => {
  const status = globalMaintenanceStatus.value
  if (!status?.maintenance) return false

  // If it's currently active, it's not "scheduled but not active"
  if (isMaintenanceCurrentlyActive(status)) return false

  // If there's no start date, it's indefinite (not scheduled)
  if (!status.start_date) return false

  // Check if start date is in the future
  const now = new Date()
  const start = new Date(status.start_date)

  return start > now
})

// Check if global maintenance is currently active
const isGlobalCurrentlyActive = computed(() => {
  const status = globalMaintenanceStatus.value
  return status ? isMaintenanceCurrentlyActive(status) : false
})

// Handle cancel scheduled maintenance
const handleCancelScheduledMaintenance = async () => {
  const confirm = useConfirm()

  const action = 'キャンセル'
  const actionDescription
    = 'スケジュールされたメンテナンスをキャンセルします。'

  confirm.show({
    title: `メンテナンス${action}の確認`,
    description: `テナント「${props.tenant?.description}」の${actionDescription}`,
    confirmText: action,
    cancelText: 'キャンセル',
    onConfirm: async () => {
      try {
        await maintenanceStore.deactivateTenant(props.tenant?.id)

        // Show success notification
        const toast = useToast()
        toast.add({
          title: 'メンテナンス予定キャンセル完了',
          description: `テナント「${props.tenant?.description}」のメンテナンス予定をキャンセルしました`,
          color: 'green'
        })
      } catch (error) {
        console.error('Failed to cancel scheduled maintenance:', error)
      }
    }
  })
}

// Handle maintenance expiration or start
const handleMaintenanceExpired = async () => {
  // Refresh maintenance status to get updated state
  if (props.tenant?.id) {
    await maintenanceStore.fetchMaintenanceStatus(props.tenant.id)
  }

  // Check if this is a start or end event
  const status = currentStatus.value
  if (status) {
    const now = new Date()
    const isCurrentlyActive = isMaintenanceCurrentlyActive(status)

    if (isCurrentlyActive && status.start_date) {
      const start = new Date(status.start_date)
      // If we just passed the start time, this is a start event
      if (Math.abs(now.getTime() - start.getTime()) < 60000) {
        // Within 1 minute
        const toast = useToast()
        toast.add({
          title: 'メンテナンス開始',
          description: `テナント「${props.tenant?.description}」のメンテナンスが開始されました`,
          color: 'orange'
        })
        return
      }
    }
  }

  // Default to end notification
  const toast = useToast()
  toast.add({
    title: 'メンテナンス終了',
    description: `テナント「${props.tenant?.description}」のメンテナンス期間が終了しました`,
    color: 'green'
  })
}

// Handle refresh all maintenance status
const handleRefreshAllStatus = async () => {
  try {
    await maintenanceStore.checkSystemMaintenance()

    // Show success notification
    const toast = useToast()
    toast.add({
      title: '状態更新完了',
      description: 'システムメンテナンス状態を更新しました',
      color: 'green'
    })
  } catch (error) {
    console.error('Failed to refresh system maintenance status:', error)

    // Show error notification
    const toast = useToast()
    toast.add({
      title: '状態更新エラー',
      description: 'メンテナンス状態の更新に失敗しました',
      color: 'red'
    })
  }
}

const isStartDateAfterNow = computed(() => {
  if (!maintenanceForm.start_date) return false
  const startDate = new Date(maintenanceForm.start_date)
  const now = new Date()
  return startDate > now && maintenanceForm.mode === 'scheduled'
})
</script>

<template>
  <div class="space-y-6">
    <!-- Tenant-specific maintenance control -->
    <div v-if="canManageTenant">
      <div class="flex items-center justify-between">
        <UDashboardSection
          title="チャットボットのメンテナンス設定"
          description="チャットボットのメンテナンス設定を行います。"
        />
        <div class="flex items-center gap-3">
          <UBadge
            size="lg"
            :ui="{
              rounded: 'rounded-full'
            }"
            :color="getStatusBadgeColor()"
            :label="getStatusBadgeLabel()"
          />
          <!-- Countdown for maintenance (active or scheduled) -->
          <div
            v-if="isActive || isScheduledButNotActive"
            class="flex flex-col gap-1"
          >
            <MaintenanceCountdown
              v-if="currentStatus"
              :end-date="currentStatus.end_date"
              :start-date="currentStatus.start_date"
              :message="getCountdownMessage(currentStatus)"
              :indefinite="!currentStatus.end_date"
              :is-active="isActive"
              size="sm"
              :variant="isActive ? 'warning' : 'default'"
              @expired="handleMaintenanceExpired"
            />
          </div>
        </div>
      </div>

      <div class="space-y-6">
        <!-- Scheduled Maintenance Info (when maintenance is scheduled but not active) -->
        <div
          v-if="isScheduledButNotActive"
          class="space-y-4"
        >
          <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
            <div class="flex items-start justify-between mb-3">
              <h5 class="font-medium text-blue-900">
                メンテナンス予定
              </h5>
              <UBadge
                color="blue"
                label="予定"
                size="xs"
              />
            </div>

            <div
              v-if="currentStatus"
              class="text-sm text-blue-700 space-y-2"
            >
              <div
                v-if="currentStatus.message"
                class="bg-white/50 p-2 rounded"
              >
                <span class="font-medium">メッセージ:</span>
                <div class="mt-1 text-gray-800 whitespace-break-spaces">
                  {{ currentStatus.message }}
                </div>
              </div>

              <p v-if="currentStatus.start_date">
                <span class="font-medium">開始予定:</span>
                {{ formatDate(currentStatus.start_date) }}
              </p>
              <p v-if="currentStatus.end_date">
                <span class="font-medium">終了予定:</span>
                {{ formatDate(currentStatus.end_date) }}
              </p>

              <!-- Countdown to start -->
              <div class="mt-3 p-2 bg-blue-100 rounded">
                <MaintenanceCountdown
                  :end-date="currentStatus.end_date"
                  :start-date="currentStatus.start_date"
                  :message="'開始まで'"
                  :indefinite="false"
                  :is-active="false"
                  size="sm"
                  variant="default"
                  :show-icon="true"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Maintenance Configuration Form -->
        <div
          v-else-if="!isActive && !isScheduledButNotActive"
          class="space-y-6"
        >
          <div>
            <!-- Maintenance Mode Selection -->
            <UFormGroup
              label="メンテナンスモード"
              description="メンテナンスの種類を選択してください"
              class="mb-4"
            >
              <div class="flex flex-row gap-8">
                <BaseCheckboxButton
                  :model-value="maintenanceForm.mode === 'indefinite'"
                  data-tour="maintenance-indefinite"
                  class="flex-1"
                  label="無期限メンテナンス"
                  help="手動で終了するまで継続"
                  :disabled="false"
                  @update:model-value="
                    () => (maintenanceForm.mode = 'indefinite')
                  "
                />
                <BaseCheckboxButton
                  :model-value="maintenanceForm.mode === 'scheduled'"
                  data-tour="maintenance-scheduled"
                  class="flex-1"
                  label="スケジュールメンテナンス"
                  help="指定した時間で自動終了"
                  :disabled="false"
                  @update:model-value="
                    () => (maintenanceForm.mode = 'scheduled')
                  "
                />
              </div>
            </UFormGroup>

            <!-- Message Input -->
            <UFormGroup
              label="メンテナンスメッセージ"
              description="ユーザーに表示されるメンテナンスメッセージ"
              class="mb-4"
            >
              <UTextarea
                v-model="maintenanceForm.message"
                placeholder="ただいまメンテナンス中です。しばらくしてもう一度お試しください。"
                :rows="3"
                autoresize
              />
            </UFormGroup>

            <!-- Timezone Information Alert -->
            <UAlert
              v-if="maintenanceForm.mode === 'scheduled'"
              color="blue"
              variant="soft"
              title="タイムゾーンについて"
              description="すべての日時は日本標準時（JST/UTC+9）で入力・表示・処理されます。"
              class="mb-4"
            />

            <!-- Scheduled Maintenance Date Inputs -->
            <div
              v-if="maintenanceForm.mode === 'scheduled'"
              class="grid grid-cols-1 md:grid-cols-2 gap-4"
            >
              <UFormGroup
                label="開始日時"
                description="メンテナンス開始日時（日本時間 JST/UTC+9）"
                :error="startDateError"
              >
                <UInput
                  v-model="maintenanceForm.start_date"
                  type="datetime-local"
                />
                <template #help>
                  <div
                    class="flex items-center gap-1 text-xs text-blue-600 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-information-circle"
                      class="w-3 h-3"
                    />
                    <span>日本標準時（JST）で入力してください</span>
                  </div>
                </template>
              </UFormGroup>

              <UFormGroup
                label="終了日時"
                description="メンテナンス終了日時（日本時間 JST/UTC+9）"
                :error="endDateError"
              >
                <UInput
                  v-model="maintenanceForm.end_date"
                  type="datetime-local"
                />
                <template #help>
                  <div
                    class="flex items-center gap-1 text-xs text-blue-600 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-information-circle"
                      class="w-3 h-3"
                    />
                    <span>日本標準時（JST）で入力してください</span>
                  </div>
                </template>
              </UFormGroup>
            </div>

            <!-- Indefinite Maintenance Info -->
            <UAlert
              v-if="maintenanceForm.mode === 'indefinite'"
              color="amber"
              variant="soft"
              title="無期限メンテナンス"
              description="このメンテナンスは手動で終了するまで継続されます。終了するには「メンテナンス無効化」ボタンを使用してください。"
              class="mt-4"
            />
          </div>
        </div>
        <!-- Status details -->
        <div
          v-if="currentStatus"
          class="space-y-3"
        >
          <!-- Current maintenance info -->
          <div
            v-if="isActive"
            class="bg-red-50 p-4 rounded-lg border-l-4 border-red-400"
          >
            <div class="flex items-start justify-between mb-3">
              <h5 class="font-medium text-red-900">
                現在のメンテナンス情報
              </h5>
              <UBadge
                :color="currentStatus?.end_date ? 'blue' : 'orange'"
                :label="currentStatus?.end_date ? 'スケジュール' : '無期限'"
                size="xs"
              />
            </div>

            <div class="text-sm text-red-700 space-y-2">
              <div
                v-if="currentStatus.message"
                class="bg-white/50 p-2 rounded"
              >
                <span class="font-medium">メッセージ:</span>
                <div class="mt-1 text-gray-800 whitespace-break-spaces">
                  {{ currentStatus.message }}
                </div>
              </div>

              <!-- Scheduled maintenance info -->
              <template v-if="currentStatus.end_date">
                <p v-if="currentStatus.start_date">
                  <span class="font-medium">開始日時:</span>
                  {{ formatDate(currentStatus.start_date) }}
                </p>
                <p>
                  <span class="font-medium">終了予定:</span>
                  {{ formatDate(currentStatus.end_date) }}
                </p>
              </template>

              <!-- Indefinite maintenance info -->
              <template v-else>
                <div class="bg-orange-50 p-2 rounded border border-orange-200">
                  <div class="flex items-center gap-2 text-orange-800">
                    <UIcon
                      name="i-heroicons-exclamation-triangle"
                      class="w-4 h-4"
                    />
                    <span class="font-medium">無期限メンテナンス</span>
                  </div>
                  <p class="text-xs text-orange-700 mt-1">
                    このメンテナンスは手動で終了するまで継続されます
                  </p>
                  <p
                    v-if="currentStatus.start_date"
                    class="text-xs text-orange-600 mt-1"
                  >
                    開始: {{ formatDate(currentStatus.start_date) }}
                  </p>
                </div>
              </template>
            </div>
          </div>
        </div>
        <!-- Control Section -->
        <div class="space-y-4">
          <!-- Action Buttons -->
          <div class="flex items-center gap-3">
            <!-- For scheduled but not active maintenance -->
            <UButton
              v-if="isScheduledButNotActive"
              :loading="loadings.deactivateTenant"
              color="yellow"
              size="lg"
              icon="i-heroicons-x-mark"
              class="w-1/2 justify-center"
              @click="handleCancelScheduledMaintenance"
            >
              予定キャンセル
            </UButton>

            <!-- For no maintenance or expired maintenance -->
            <UButton
              v-else-if="!isActive && !isScheduledButNotActive"
              :loading="loadings.activateTenant"
              :color="isStartDateAfterNow ? 'blue' : 'red'"
              size="lg"
              :icon="
                isStartDateAfterNow
                  ? 'akar-icons:schedule'
                  : 'i-heroicons-exclamation-triangle'
              "
              class="w-1/2 justify-center"
              @click="handleToggleTenant"
            >
              {{
                isStartDateAfterNow
                  ? "メンテナンスの計画を開始"
                  : "メンテナンス開始"
              }}
            </UButton>

            <!-- For active maintenance -->
            <UButton
              v-else-if="isActive"
              :loading="loadings.deactivateTenant"
              color="green"
              size="lg"
              icon="i-heroicons-check-circle"
              class="w-1/2 justify-center ml-auto"
              @click="handleToggleTenant"
            >
              メンテナンス終了
            </UButton>

            <!-- Quick Actions -->
            <!-- <template v-if="isActive || isScheduledButNotActive">
              <UButton
                color="gray"
                variant="outline"
                size="lg"
                icon="i-heroicons-arrow-path"
                :loading="loadings.fetchMaintenanceStatus"
                @click="
                  () => maintenanceStore.fetchMaintenanceStatus(tenant?.id)
                "
              >
                状態更新
              </UButton>
            </template> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
