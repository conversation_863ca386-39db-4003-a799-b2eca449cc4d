<script setup lang="ts">
import type { PropType } from 'vue'
import type { TenantWithEnvironments } from '~/types/tenant'
import { BaseStatusToggleNoConfirm } from '#components'

const { hasPermission, PERMISSIONS } = useAppPermissions()

const props = defineProps({
  tenants: {
    type: Array as PropType<TenantWithEnvironments[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  tenantsPagination: {
    type: Object,
    required: true
  },
  tenantsTotal: {
    type: Number,
    required: true
  },
  tenantsFilter: {
    type: Object,
    required: true
  },
  selectedTenants: {
    type: Array as PropType<TenantWithEnvironments[]>,
    default: () => []
  }
})

const emit = defineEmits([
  'edit',
  'delete',
  'create',
  'refresh',
  'deleteMany',
  'update:selectedTenants',
  'updateMaintenance',
  'toggleTenantStatus'
])

const settingsStore = useSettingsStore()

const onUpdateMaintenance = async (
  tenantId: string,
  envId: string,
  key: string,
  value: boolean
) => {
  await settingsStore.updateCustomSetting(tenantId, envId, key, value)
  emit('updateMaintenance', { tenantId, envId, key, value })
}

function getItems(tenant: TenantWithEnvironments) {
  const items = [[]] as any

  // Only add edit option if user has permission
  if (hasPermission(PERMISSIONS.EDIT_TENANT)) {
    items[0].push({
      label: 'テナント編集',
      click: () => emit('edit', tenant)
    })
  }

  // Add enable/disable option if user has permission
  if (hasPermission(PERMISSIONS.EDIT_TENANT)) {
    items[0].push({
      label: tenant.enabled ? '無効化' : '有効化',
      click: () => emit('toggleTenantStatus', tenant)
    })
  }

  // Only add delete option if user has permission
  if (hasPermission(PERMISSIONS.DELETE_TENANT)) {
    items[0].push({
      label: 'テナント削除',
      labelClass: 'text-red-500 dark:text-red-400',
      click: () => emit('delete', tenant)
    })
  }
  return items
}

const defaultColumns = [
  {
    key: 'id',
    label: 'テナントID',
    sortable: true
  },
  {
    key: 'environments',
    label: '環境',
    sortable: false
  },
  {
    key: 'enabled',
    label: 'ステータス',
    sortable: false,
    class: 'text-center min-w-36 w-0'
  }
]

const items = [
  [
    {
      label: '一括削除',
      icon: 'carbon:batch-job',
      click: () => {
        emit('deleteMany')
      }
    }
  ]
]
</script>

<template>
  <UCard
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: '!px-3 !py-3' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700'
      },
      footer: { padding: 'p-4' }
    }"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <h2
          class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
        >
          テナント一覧
          <UBadge
            v-if="tenantsTotal"
            :label="tenantsTotal"
          />
        </h2>
        <!-- Use slot for actions to allow parent component to control permissions -->
        <slot name="actions">
          <UButton
            label="新規作成"
            icon="i-heroicons-plus"
            color="gray"
            size="sm"
            @click="emit('create')"
          />
        </slot>
      </div>
    </template>

    <!-- Filters -->
    <div class="flex items-center justify-between gap-3 px-4 py-3">
      <div class="flex gap-5 items-center">
        <UInput
          v-model="props.tenantsFilter.id"
          icon="i-heroicons-magnifying-glass-20-solid"
          autocomplete="off"
          placeholder="テナントIDの検索"
          @keydown.esc="$event.target.blur()"
        >
          <template #trailing>
            <UKbd value="/" />
          </template>
        </UInput>

        <UInput
          v-model="props.tenantsFilter.description"
          icon="i-heroicons-magnifying-glass-20-solid"
          autocomplete="off"
          placeholder="説明の検索"
          @keydown.esc="$event.target.blur()"
        />
      </div>
      <div class="flex items-center gap-1.5">
        <div v-if="selectedTenants.length">
          <UDropdown
            :items="items"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              color="white"
              :label="`一括操作（${selectedTenants.length}件）`"
              icon="fluent:form-multiple-20-regular"
              trailing-icon="i-heroicons-chevron-down-20-solid"
              size="sm"
            />
          </UDropdown>
        </div>
        <UButton
          icon="prime:sync"
          color="gray"
          size="sm"
          @click="emit('refresh')"
        />
      </div>
    </div>

    <UTable
      v-if="tenants"
      :rows="tenants"
      :columns="defaultColumns"
      class="w-full"
      :ui="{
        divide: 'divide-gray-200 dark:divide-gray-800',
        tr: { base: 'group' }
      }"
      :loading="loading"
      @update:model-value="$emit('update:selectedTenants', $event)"
    >
      <template #id-data="{ row }">
        <div class="flex items-center gap-3 min-w-0">
          <UAvatar
            v-bind="row.avatar"
            size="sm"
            :alt="row.description?.toUpperCase()"
            :ui="{
              rounded: 'rounded-lg'
            }"
          />
          <div class="text-sm min-w-0">
            <p class="text-gray-900 dark:text-white font-medium truncate">
              {{ row.id }}
            </p>
            <div class="text-sm text-gray-500 dark:text-gray-400 truncate">
              {{ row.description }}
            </div>
          </div>
        </div>
      </template>

      <template #description-data="{ row }">
        <div class="text-sm text-gray-500 dark:text-gray-400 truncate">
          {{ row.description }}
        </div>
      </template>

      <template #environments-data="{ row }">
        <div
          v-if="!row.environments?.length"
          class="text-gray-400"
        >
          環境なし
        </div>
        <div
          v-else
          class="flex flex-col gap-2"
        >
          <div class="flex flex-row gap-2">
            <div
              v-for="env in row.environments"
              :key="env.id"
              class="flex flex-row gap-3 text-xs items-center"
            >
              <UBadge
                :icon="environmentObject(env.environment)?.icon"
                size="xs"
                :color="environmentObject(env.environment)?.color"
                variant="subtle"
                :label="environmentObject(env.environment)?.label"
                :trailing="false"
              >
                <div class="flex flex-wrap flex-row gap-1 items-center px-2">
                  <div>
                    {{ environmentObject(env.environment)?.label }}
                  </div>
                  <div>(v.{{ env.version }})</div>
                </div>
              </UBadge>
            </div>
          </div>
        </div>
      </template>

      <template #enabled-data="{ row }">
        <div class="flex items-center gap-3 justify-between">
          <BaseStatusToggleNoConfirm
            v-model="row.enabled"
            class="capitalize flex-1 justify-center max-w-16"
            @toggle="emit('toggleTenantStatus', row)"
          />

          <UDropdown
            class="group-hover:block"
            :class="{
              block: loading,
              hidden: !loading
            }"
            :items="getItems(row)"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              class="row-menu"
              color="white"
              icon="charm:menu-meatball"
              size="xs"
              square
              :loading="loading"
            />
          </UDropdown>
        </div>
      </template>
    </UTable>

    <!-- Number of rows & Pagination -->
    <template #footer>
      <div class="flex flex-wrap justify-between items-center">
        <div class="flex items-center gap-1.5">
          <span class="text-sm leading-5">表示件数:</span>

          <USelect
            v-model="tenantsPagination.pageCount"
            :options="[3, 5, 10, 20, 30, 40]"
            class="w-20"
          />
        </div>

        <UPagination
          v-model="tenantsPagination.page"
          :page-count="tenantsPagination.pageCount"
          :total="tenantsTotal"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline'
              }
            }
          }"
        />
      </div>
    </template>
  </UCard>
</template>
