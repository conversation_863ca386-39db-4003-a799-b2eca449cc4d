// Prompt types enum
export enum PromptType {
  BASIC_QUERY = 1,
  RAG_QUERY = 2,
  ANALYZE = 3,
  REFINE = 4,
  INPUT_CHECKER = 5,
  REPLY_ANALYZE = 6,
  REFERENCE_URL = 10,
  CONTEXT_USEFUL = 11,
  WEATHER_QUERY = 101,
  QUERY_TRANSLATE = 121,
  ANSWER_TRANSLATE = 122
}

// Prompt type labels mapping
export const PROMPT_TYPE_LABELS: Record<number, string> = {
  1: 'Basic Query',
  2: 'RAG Query',
  3: 'Analyze',
  4: 'Refine',
  5: 'Input Checker',
  6: 'Reply Analyze',
  10: 'Reference URL',
  11: 'Context Useful',
  101: 'Weather Query',
  121: 'Query Translate',
  122: 'Answer Translate'
}
